const { ethers } = require('ethers');
require('dotenv').config();

// Contract addresses from .env
const SECONDARY_MARKET_ADDRESS = process.env.VITE_SECONDARY_MARKET_ADDRESS;
const USDT_ADDRESS = process.env.VITE_USDT_ADDRESS;
const BLOCKS_ADDRESS = process.env.VITE_SHARE_ADDRESS;

// RPC URL
const RPC_URL = process.env.VITE_RPC_URL || 'https://data-seed-prebsc-1-s1.binance.org:8545/';

// SecondaryMarket ABI - minimal for testing
const SECONDARY_MARKET_ABI = [
  'function targetPrice() external view returns (uint256)',
  'function swapFee() external view returns (uint256)',
  'function paused() external view returns (bool)',
  'function getSwapQuote(uint256 blocksAmount) external view returns (uint256 usdtAmount)',
  'function factory() external view returns (address)',
  'function router() external view returns (address)',
  'function token() external view returns (address)',
  'function usdtToken() external view returns (address)',
  'function feeRecipient() external view returns (address)'
];

async function testSecondaryMarket() {
  console.log('🧪 Testing SecondaryMarket Contract Functions');
  console.log('=' .repeat(50));
  
  try {
    // Create provider
    const provider = new ethers.JsonRpcProvider(RPC_URL);
    console.log('✅ Connected to BSC Testnet');
    
    // Create contract instance
    const secondaryMarket = new ethers.Contract(
      SECONDARY_MARKET_ADDRESS,
      SECONDARY_MARKET_ABI,
      provider
    );
    
    console.log('📋 Contract Address:', SECONDARY_MARKET_ADDRESS);
    console.log('');
    
    // Test basic read functions
    console.log('📊 Testing Read Functions:');
    
    try {
      const targetPrice = await secondaryMarket.targetPrice();
      console.log('✅ targetPrice():', ethers.formatUnits(targetPrice, 18), 'USDT per BLOCKS');
    } catch (err) {
      console.log('❌ targetPrice() failed:', err.message);
    }
    
    try {
      const swapFee = await secondaryMarket.swapFee();
      console.log('✅ swapFee():', swapFee.toString(), 'basis points');
    } catch (err) {
      console.log('❌ swapFee() failed:', err.message);
    }
    
    try {
      const isPaused = await secondaryMarket.paused();
      console.log('✅ paused():', isPaused);
    } catch (err) {
      console.log('❌ paused() failed:', err.message);
    }
    
    try {
      const usdtToken = await secondaryMarket.usdtToken();
      console.log('✅ usdtToken():', usdtToken);
      console.log('   Expected USDT:', USDT_ADDRESS);
      console.log('   Match:', usdtToken.toLowerCase() === USDT_ADDRESS.toLowerCase() ? '✅' : '❌');
    } catch (err) {
      console.log('❌ usdtToken() failed:', err.message);
    }
    
    try {
      const blocksToken = await secondaryMarket.token();
      console.log('✅ token() (BLOCKS):', blocksToken);
      console.log('   Expected BLOCKS:', BLOCKS_ADDRESS);
      console.log('   Match:', blocksToken.toLowerCase() === BLOCKS_ADDRESS.toLowerCase() ? '✅' : '❌');
    } catch (err) {
      console.log('❌ token() failed:', err.message);
    }
    
    try {
      const router = await secondaryMarket.router();
      console.log('✅ router():', router);
    } catch (err) {
      console.log('❌ router() failed:', err.message);
    }
    
    try {
      const factory = await secondaryMarket.factory();
      console.log('✅ factory():', factory);
    } catch (err) {
      console.log('❌ factory() failed:', err.message);
    }
    
    try {
      const feeRecipient = await secondaryMarket.feeRecipient();
      console.log('✅ feeRecipient():', feeRecipient);
    } catch (err) {
      console.log('❌ feeRecipient() failed:', err.message);
    }
    
    // Test quote function
    console.log('');
    console.log('💱 Testing Quote Function:');
    
    try {
      const testBlocksAmount = ethers.parseUnits('1', 18); // 1 BLOCKS
      const usdtQuote = await secondaryMarket.getSwapQuote(testBlocksAmount);
      console.log('✅ getSwapQuote(1 BLOCKS):', ethers.formatUnits(usdtQuote, 18), 'USDT');
    } catch (err) {
      console.log('❌ getSwapQuote() failed:', err.message);
    }
    
    console.log('');
    console.log('🎉 SecondaryMarket contract test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testSecondaryMarket().catch(console.error);
